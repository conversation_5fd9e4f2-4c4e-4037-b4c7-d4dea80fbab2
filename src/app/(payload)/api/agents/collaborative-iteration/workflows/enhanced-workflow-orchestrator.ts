import { v4 as uuidv4 } from 'uuid';
import {
  IterativeCollaborationState,
  IterativeMessageType,
  AgentId,
  ArtifactStatus,
  Goal,
  GoalStatus
} from '../types';
import { EnhancedIterativeMessage, createEnhancedIterativeMessage } from '../types/enhanced-message';
import { stateStore } from '../utils/stateStore';
import logger from '../utils/logger';
import { enhancedMessageBus } from '../utils/enhancedMessageBus';

/**
 * Quality thresholds for different phases
 */
export const QUALITY_THRESHOLDS = {
  MARKET_RESEARCH: 0.75,
  KEYWORD_RESEARCH: 0.75,
  CONTENT_STRATEGY: 0.80,
  CONTENT_GENERATION: 0.85,
  SEO_OPTIMIZATION: 0.85
};

/**
 * Initiate the enhanced collaborative workflow
 */
export async function initiateEnhancedCollaborativeWorkflow(
  sessionId: string,
  initialTopic: string,
  initialParams: any = {}
): Promise<boolean> {
  try {
    // Extract parameters
    const {
      contentType = 'blog-article',
      targetAudience = 'general audience',
      tone = 'informative',
      keywords = []
    } = initialParams;

    // Create discussion ID
    const discussionId = `discussion-${uuidv4()}`;

    // Create system message to initialize the workflow
    const systemMessage = createEnhancedIterativeMessage(
      'system',
      'all',
      IterativeMessageType.SYSTEM_MESSAGE,
      {
        event: 'WORKFLOW_INITIALIZATION',
        details: {
          topic: initialTopic,
          contentType,
          targetAudience,
          tone,
          keywords
        }
      },
      {
        sessionId,
        conversationId: discussionId,
        reasoning: {
          thoughts: ['Initializing collaborative workflow for content creation'],
          considerations: [
            'Need to coordinate multiple specialized agents',
            'Must ensure quality at each phase',
            'Should track progress and artifacts'
          ],
          decision: 'Initialize workflow with system parameters',
          confidence: 0.95
        },
        metadata: {
          workflowPhase: 'initialization'
        }
      }
    );

    // Initialize workflow goals
    const workflowGoals: Goal[] = [
      {
        id: 'market-research-goal',
        description: 'Complete market research analysis',
        status: 'pending',
        assignedTo: AgentId.MARKET_RESEARCH,
        dependencies: [],
        artifactTypes: ['market-research-report'],
        qualityThreshold: QUALITY_THRESHOLDS.MARKET_RESEARCH
      },
      {
        id: 'keyword-research-goal',
        description: 'Complete SEO keyword analysis',
        status: 'pending',
        assignedTo: AgentId.SEO_KEYWORD,
        dependencies: ['market-research-goal'],
        artifactTypes: ['keyword-analysis'],
        qualityThreshold: QUALITY_THRESHOLDS.KEYWORD_RESEARCH
      },
      {
        id: 'content-strategy-goal',
        description: 'Develop content strategy',
        status: 'pending',
        assignedTo: AgentId.CONTENT_STRATEGY,
        dependencies: ['market-research-goal', 'keyword-research-goal'],
        artifactTypes: ['content-strategy'],
        qualityThreshold: QUALITY_THRESHOLDS.CONTENT_STRATEGY
      },
      {
        id: 'content-generation-goal',
        description: 'Generate initial content draft',
        status: 'pending',
        assignedTo: AgentId.CONTENT_GENERATION,
        dependencies: ['content-strategy-goal'],
        artifactTypes: ['content-draft'],
        qualityThreshold: QUALITY_THRESHOLDS.CONTENT_GENERATION
      },
      {
        id: 'seo-optimization-goal',
        description: 'Optimize content for SEO',
        status: 'pending',
        assignedTo: AgentId.SEO_OPTIMIZATION,
        dependencies: ['content-generation-goal'],
        artifactTypes: ['seo-optimized-content'],
        qualityThreshold: QUALITY_THRESHOLDS.SEO_OPTIMIZATION
      }
    ];

    // Initialize or update the state
    let state = await stateStore.getState(sessionId);
    if (!state) {
      // Initialize a new state with A2A protocol compatibility
      state = {
        id: sessionId,
        topic: initialTopic,
        contentType,
        targetAudience,
        tone,
        keywords,
        status: 'active',
        startTime: new Date().toISOString(),
        artifacts: {},
        generatedArtifacts: [],
        consultations: {},
        agentStates: {},
        messages: [],
        goals: workflowGoals,
        currentPhase: 'initialization',
        workflowProgress: {
          marketResearchComplete: false,
          keywordResearchComplete: false,
          contentStrategyComplete: false,
          contentGenerationComplete: false,
          seoOptimizationComplete: false,
          currentPhase: 'initialization'
        },
        discussions: {
          [discussionId]: {
            id: discussionId,
            topic: `Collaborative content creation for "${initialTopic}"`,
            participants: Object.values(AgentId),
            leadAgent: AgentId.CONTENT_STRATEGY,
            status: 'active',
            messages: [],
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
          }
        },
        metadata: {
          a2aCompatible: true,
          enhancedWorkflow: true,
          initialParams
        }
      };

      // Store the initial state
      await stateStore.setState(sessionId, state);
    }

    // Send the system message
    await enhancedMessageBus.sendMessage(sessionId, systemMessage);

    // Begin the research phase with a slight delay
    setTimeout(() => {
      // Log phase transition
      logger.info(`Transitioning from initialization to research phase`, {
        sessionId,
        fromPhase: 'initialization',
        toPhase: 'research',
        timestamp: new Date().toISOString()
      });

      // Start the research phase
      initiateResearchPhase(
        sessionId,
        initialTopic,
        contentType,
        targetAudience,
        tone,
        {
          discussionId,
          keywords,
          ...initialParams
        }
      ).catch((err: Error) => {
        logger.error(`Error in research phase`, {
          sessionId,
          phase: 'research',
          error: err.message || String(err),
          stack: err.stack
        });
      });
    }, 2000);

    return true;
  } catch (error) {
    const err = error as Error;
    logger.error(`Error initiating enhanced collaborative workflow`, {
      sessionId,
      phase: 'initialization',
      error: err.message || String(error),
      stack: err.stack
    });
    return false;
  }
}

/**
 * Transition from research phase to content generation phase
 */
export async function transitionToContentGeneration(
  sessionId: string,
  topic: string,
  contentType: string,
  targetAudience: string,
  tone: string,
  additionalParams: Record<string, any> = {}
): Promise<boolean> {
  try {
    // Get current state
    const state = await stateStore.getState(sessionId);
    if (!state) {
      throw new Error(`Session ${sessionId} not found`);
    }

    // Update workflow progress
    const updatedState = {
      ...state,
      currentPhase: 'content-generation',
      workflowProgress: {
        ...state.workflowProgress,
        marketResearchComplete: true,
        keywordResearchComplete: true,
        contentStrategyComplete: true,
        currentPhase: 'content-generation'
      }
    };

    // Store updated state
    await stateStore.setState(sessionId, updatedState);

    // Log phase transition
    logger.info(`Transitioning from research to content-generation phase`, {
      sessionId,
      fromPhase: 'research',
      toPhase: 'content-generation',
      timestamp: new Date().toISOString()
    });

    // Create phase transition message
    const transitionMessage = createEnhancedIterativeMessage(
      'system',
      'all',
      IterativeMessageType.SYSTEM_MESSAGE,
      {
        event: 'PHASE_TRANSITION',
        details: {
          fromPhase: 'research',
          toPhase: 'content-generation',
          completedGoals: ['market-research-goal', 'keyword-research-goal', 'content-strategy-goal'],
          nextGoals: ['content-generation-goal']
        }
      },
      {
        sessionId,
        conversationId: state.discussions ? Object.keys(state.discussions)[0] : sessionId,
        reasoning: {
          thoughts: ['Research phase completed successfully'],
          considerations: [
            'Market research artifacts meet quality threshold',
            'Keyword research artifacts meet quality threshold',
            'Content strategy artifacts meet quality threshold'
          ],
          decision: 'Transition to content generation phase',
          confidence: 0.9
        },
        metadata: {
          workflowPhase: 'transition'
        }
      }
    );

    // Send transition message
    await enhancedMessageBus.sendMessage(sessionId, transitionMessage);

    // Start content generation phase
    return await initiateContentGenerationPhase(
      sessionId,
      topic,
      contentType,
      targetAudience,
      tone,
      additionalParams
    );
  } catch (error) {
    const err = error as Error;
    logger.error(`Error transitioning to content generation phase`, {
      sessionId,
      error: err.message || String(error),
      stack: err.stack
    });
    return false;
  }
}

/**
 * Transition from content generation phase to review phase
 */
export async function transitionToReviewPhase(
  sessionId: string,
  topic: string,
  contentType: string,
  targetAudience: string,
  tone: string,
  additionalParams: Record<string, any> = {}
): Promise<boolean> {
  try {
    // Get current state
    const state = await stateStore.getState(sessionId);
    if (!state) {
      throw new Error(`Session ${sessionId} not found`);
    }

    // Update workflow progress
    const updatedState = {
      ...state,
      currentPhase: 'review',
      workflowProgress: {
        ...state.workflowProgress,
        contentGenerationComplete: true,
        currentPhase: 'review'
      }
    };

    // Store updated state
    await stateStore.setState(sessionId, updatedState);

    // Log phase transition
    logger.info(`Transitioning from content-generation to review phase`, {
      sessionId,
      fromPhase: 'content-generation',
      toPhase: 'review',
      timestamp: new Date().toISOString()
    });

    // Create phase transition message
    const transitionMessage = createEnhancedIterativeMessage(
      'system',
      'all',
      IterativeMessageType.SYSTEM_MESSAGE,
      {
        event: 'PHASE_TRANSITION',
        details: {
          fromPhase: 'content-generation',
          toPhase: 'review',
          completedGoals: ['content-generation-goal'],
          nextGoals: ['seo-optimization-goal']
        }
      },
      {
        sessionId,
        conversationId: state.discussions ? Object.keys(state.discussions)[0] : sessionId,
        reasoning: {
          thoughts: ['Content generation phase completed successfully'],
          considerations: [
            'Content draft meets quality threshold',
            'Ready for SEO optimization and final review'
          ],
          decision: 'Transition to review phase',
          confidence: 0.9
        },
        metadata: {
          workflowPhase: 'transition'
        }
      }
    );

    // Send transition message
    await enhancedMessageBus.sendMessage(sessionId, transitionMessage);

    // Import the review phase implementation
    const { initiateEnhancedReviewPhase } = require('./enhanced-review-phase');

    // Start review phase
    return await initiateEnhancedReviewPhase(
      sessionId,
      topic,
      contentType,
      targetAudience,
      tone,
      additionalParams
    );
  } catch (error) {
    const err = error as Error;
    logger.error(`Error transitioning to review phase`, {
      sessionId,
      error: err.message || String(error),
      stack: err.stack
    });
    return false;
  }
}

// Import phase implementations
const { initiateEnhancedResearchPhase } = require('./enhanced-research-phase');
const { initiateEnhancedContentGenerationPhase } = require('./enhanced-content-generation');

// Alias functions for internal use
async function initiateResearchPhase(
  sessionId: string,
  topic: string,
  contentType: string,
  targetAudience: string,
  tone: string,
  additionalParams: Record<string, any> = {}
): Promise<boolean> {
  return await initiateEnhancedResearchPhase(
    sessionId,
    topic,
    contentType,
    targetAudience,
    tone,
    additionalParams
  );
}

async function initiateContentGenerationPhase(
  sessionId: string,
  topic: string,
  contentType: string,
  targetAudience: string,
  tone: string,
  additionalParams: Record<string, any> = {}
): Promise<boolean> {
  return await initiateEnhancedContentGenerationPhase(
    sessionId,
    topic,
    contentType,
    targetAudience,
    tone,
    additionalParams
  );
}
