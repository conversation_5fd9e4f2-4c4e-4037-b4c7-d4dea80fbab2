import { v4 as uuidv4 } from 'uuid';
import {
  IterativeMessageType,
  AgentId,
  ArtifactStatus,
  Goal,
  GoalStatus
} from '../types';
import { EnhancedIterativeMessage, createEnhancedIterativeMessage } from '../types/enhanced-message';
import { stateStore } from '../utils/stateStore';
import { enhancedMessageBus } from '../utils/enhancedMessageBus';
import { validationManager } from '../utils/validation-manager';
import { artifactManager } from '../utils/artifactManager';
import { consultationManager } from '../utils/consultation-manager';
import logger from '../utils/logger';
import { QUALITY_THRESHOLDS } from './enhanced-workflow-orchestrator';
import {
  contentGenerationAgent,
  contentStrategyAgent,
  seoKeywordAgent
} from '../agents';

/**
 * Initiate the enhanced content generation phase
 */
export async function initiateEnhancedContentGenerationPhase(
  sessionId: string,
  topic: string,
  contentType: string,
  targetAudience: string,
  tone: string,
  additionalParams: Record<string, any> = {}
): Promise<boolean> {
  try {
    // Get current state
    const state = await stateStore.getState(sessionId);
    if (!state) {
      throw new Error(`Session ${sessionId} not found`);
    }
    
    // Update current phase
    await stateStore.updateState(sessionId, {
      currentPhase: 'content-generation',
      workflowProgress: {
        ...state.workflowProgress,
        currentPhase: 'content-generation'
      }
    });
    
    // Get content strategy artifact
    const artifacts = await artifactManager.getArtifacts(sessionId);
    const contentStrategyArtifact = Object.values(artifacts).find(a => a.type === 'content-strategy');
    
    if (!contentStrategyArtifact) {
      throw new Error('Content strategy artifact not found. Cannot proceed with content generation.');
    }
    
    // Get keyword analysis artifact
    const keywordAnalysisArtifact = Object.values(artifacts).find(a => a.type === 'keyword-analysis');
    
    if (!keywordAnalysisArtifact) {
      logger.warn('Keyword analysis artifact not found. Proceeding with content generation without keywords.', {
        sessionId,
        topic
      });
    }
    
    // Extract keywords from keyword analysis if available
    let keywords = additionalParams.keywords || [];
    if (keywordAnalysisArtifact && keywordAnalysisArtifact.content) {
      try {
        if (keywordAnalysisArtifact.content.keywords && Array.isArray(keywordAnalysisArtifact.content.keywords)) {
          keywords = keywordAnalysisArtifact.content.keywords.map((k: any) => 
            typeof k === 'string' ? k : k.keyword || k.term || ''
          ).filter(Boolean);
        }
      } catch (error) {
        logger.warn('Error extracting keywords from keyword analysis', {
          sessionId,
          error: String(error)
        });
      }
    }
    
    // Create content generation request message
    const contentGenerationRequest = createEnhancedIterativeMessage(
      'system',
      AgentId.CONTENT_GENERATION,
      IterativeMessageType.INITIAL_REQUEST,
      {
        topic,
        contentType,
        targetAudience,
        tone,
        keywords,
        contentStrategy: contentStrategyArtifact.content,
        instructions: 'Generate high-quality content based on the content strategy'
      },
      {
        sessionId,
        conversationId: additionalParams.discussionId || sessionId,
        artifactReferences: [contentStrategyArtifact.id],
        reasoning: {
          thoughts: ['Beginning content generation phase'],
          considerations: [
            'Need to follow content strategy structure',
            'Should incorporate target keywords naturally',
            'Must align with target audience preferences and tone',
            'Should create engaging and valuable content'
          ],
          decision: 'Request content generation based on strategy and research',
          confidence: 0.9
        },
        metadata: {
          workflowPhase: 'content-generation',
          goalId: 'content-generation-goal',
          contentStrategyArtifactId: contentStrategyArtifact.id,
          keywordAnalysisArtifactId: keywordAnalysisArtifact?.id
        }
      }
    );
    
    // Send content generation request
    await enhancedMessageBus.sendMessage(sessionId, contentGenerationRequest);
    
    // Update goal status
    await validationManager.updateGoalStatus(
      sessionId,
      'content-generation-goal',
      'in-progress',
      'Content generation requested'
    );
    
    logger.info(`Content generation phase initiated`, {
      sessionId,
      topic,
      contentType,
      targetAudience,
      tone,
      timestamp: new Date().toISOString()
    });
    
    // Schedule validation of content generation phase
    setTimeout(() => {
      validateEnhancedContentGeneration(
        sessionId,
        topic,
        contentType,
        targetAudience,
        tone,
        additionalParams
      ).catch((err: Error) => {
        logger.error(`Error validating content generation phase`, {
          sessionId,
          phase: 'content-generation-validation',
          error: err.message || String(err),
          stack: err.stack
        });
      });
    }, 60000); // Check after 1 minute
    
    return true;
  } catch (error) {
    const err = error as Error;
    logger.error(`Error initiating content generation phase`, {
      sessionId,
      phase: 'content-generation',
      error: err.message || String(error),
      stack: err.stack
    });
    return false;
  }
}

/**
 * Validate the enhanced content generation phase
 */
export async function validateEnhancedContentGeneration(
  sessionId: string,
  topic: string,
  contentType: string,
  targetAudience: string,
  tone: string,
  additionalParams: Record<string, any> = {}
): Promise<boolean> {
  try {
    // Get current state
    const state = await stateStore.getState(sessionId);
    if (!state) {
      throw new Error(`Session ${sessionId} not found`);
    }
    
    // Skip if not in content generation phase
    if (state.currentPhase !== 'content-generation') {
      logger.info(`Skipping content generation validation - current phase is ${state.currentPhase}`, {
        sessionId,
        currentPhase: state.currentPhase,
        timestamp: new Date().toISOString()
      });
      return false;
    }
    
    // Validate content generation phase
    const validationResult = await validationManager.validateContentGenerationPhase(sessionId);
    
    if (validationResult.isValid) {
      logger.info(`Content generation phase validation passed`, {
        sessionId,
        timestamp: new Date().toISOString()
      });
      
      // Transition to review phase
      return await transitionToReviewPhase(
        sessionId,
        topic,
        contentType,
        targetAudience,
        tone,
        additionalParams
      );
    } else {
      logger.info(`Content generation phase validation failed`, {
        sessionId,
        details: validationResult.details,
        timestamp: new Date().toISOString()
      });
      
      // Check if content draft is missing
      if (validationResult.missingArtifacts.includes('content-draft')) {
        // If content draft is missing, request it again
        logger.info(`Content draft missing, requesting again`, {
          sessionId,
          timestamp: new Date().toISOString()
        });
        
        // Re-request content generation
        await initiateEnhancedContentGenerationPhase(
          sessionId,
          topic,
          contentType,
          targetAudience,
          tone,
          additionalParams
        );
      } else if (validationResult.lowQualityArtifacts.includes('content-draft')) {
        // If content draft is low quality, request improvement
        logger.info(`Content draft quality below threshold, requesting improvement`, {
          sessionId,
          timestamp: new Date().toISOString()
        });
        
        // Get content draft artifact
        const artifacts = await artifactManager.getArtifacts(sessionId);
        const contentDraftArtifact = Object.values(artifacts).find(a => a.type === 'content-draft');
        
        if (contentDraftArtifact) {
          // Request consultation from content strategy agent
          const consultationId = await consultationManager.requestConsultation(
            sessionId,
            'system',
            AgentId.CONTENT_STRATEGY,
            'How can this content draft be improved to better align with the content strategy?',
            {
              contentDraft: contentDraftArtifact.content,
              qualityScore: contentDraftArtifact.qualityScore
            },
            contentDraftArtifact.id
          );
          
          logger.info(`Requested content strategy consultation for content improvement`, {
            sessionId,
            consultationId,
            contentDraftId: contentDraftArtifact.id,
            timestamp: new Date().toISOString()
          });
          
          // Request consultation from SEO keyword agent
          const seoConsultationId = await consultationManager.requestConsultation(
            sessionId,
            'system',
            AgentId.SEO_KEYWORD,
            'How can this content draft be improved for better keyword optimization?',
            {
              contentDraft: contentDraftArtifact.content,
              qualityScore: contentDraftArtifact.qualityScore
            },
            contentDraftArtifact.id
          );
          
          logger.info(`Requested SEO keyword consultation for content improvement`, {
            sessionId,
            consultationId: seoConsultationId,
            contentDraftId: contentDraftArtifact.id,
            timestamp: new Date().toISOString()
          });
          
          // Request content improvement
          const contentImprovementRequest = createEnhancedIterativeMessage(
            'system',
            AgentId.CONTENT_GENERATION,
            IterativeMessageType.ITERATION_REQUEST,
            {
              artifactId: contentDraftArtifact.id,
              currentVersion: contentDraftArtifact.currentVersion,
              instructions: 'Improve the content draft based on feedback',
              consultationIds: [consultationId, seoConsultationId],
              requiredQualityScore: QUALITY_THRESHOLDS.CONTENT_GENERATION
            },
            {
              sessionId,
              conversationId: additionalParams.discussionId || sessionId,
              artifactId: contentDraftArtifact.id,
              version: contentDraftArtifact.currentVersion,
              reasoning: {
                thoughts: ['Content draft needs improvement'],
                considerations: [
                  'Quality score is below threshold',
                  'Need to incorporate feedback from content strategy and SEO experts',
                  'Must maintain alignment with overall strategy while improving quality'
                ],
                decision: 'Request content improvement with specific feedback',
                confidence: 0.9
              },
              metadata: {
                workflowPhase: 'content-generation',
                goalId: 'content-generation-goal',
                isImprovement: true
              }
            }
          );
          
          // Send content improvement request
          await enhancedMessageBus.sendMessage(sessionId, contentImprovementRequest);
        }
      }
      
      // Schedule another validation check
      setTimeout(() => {
        validateEnhancedContentGeneration(
          sessionId,
          topic,
          contentType,
          targetAudience,
          tone,
          additionalParams
        ).catch((err: Error) => {
          logger.error(`Error in scheduled content generation validation`, {
            sessionId,
            phase: 'content-generation-validation',
            error: err.message || String(err),
            stack: err.stack
          });
        });
      }, 30000); // Check again after 30 seconds
      
      return false;
    }
  } catch (error) {
    const err = error as Error;
    logger.error(`Error validating content generation phase`, {
      sessionId,
      phase: 'content-generation-validation',
      error: err.message || String(error),
      stack: err.stack
    });
    return false;
  }
}

/**
 * Transition to review phase
 * This is imported by the workflow orchestrator
 */
async function transitionToReviewPhase(
  sessionId: string,
  topic: string,
  contentType: string,
  targetAudience: string,
  tone: string,
  additionalParams: Record<string, any> = {}
): Promise<boolean> {
  try {
    // Import here to avoid circular dependency
    const { transitionToReviewPhase: orchestratorTransition } = require('./enhanced-workflow-orchestrator');
    
    return await orchestratorTransition(
      sessionId,
      topic,
      contentType,
      targetAudience,
      tone,
      additionalParams
    );
  } catch (error) {
    const err = error as Error;
    logger.error(`Error transitioning to review phase`, {
      sessionId,
      error: err.message || String(error),
      stack: err.stack
    });
    return false;
  }
}
