import { v4 as uuidv4 } from 'uuid';
import {
  IterativeMessageType,
  AgentId,
  ArtifactStatus,
  Goal,
  GoalStatus
} from '../types';
import { EnhancedIterativeMessage, createEnhancedIterativeMessage } from '../types/enhanced-message';
import { stateStore } from '../utils/stateStore';
import { enhancedMessageBus } from '../utils/enhancedMessageBus';
import { validationManager } from '../utils/validation-manager';
import { artifactManager } from '../utils/artifactManager';
import { consultationManager } from '../utils/consultation-manager';
import logger from '../utils/logger';
import { QUALITY_THRESHOLDS } from './enhanced-workflow-orchestrator';
import {
  seoOptimizationAgent,
  contentGenerationAgent,
  contentStrategyAgent
} from '../agents';

/**
 * Initiate the enhanced review phase
 */
export async function initiateEnhancedReviewPhase(
  sessionId: string,
  topic: string,
  contentType: string,
  targetAudience: string,
  tone: string,
  additionalParams: Record<string, any> = {}
): Promise<boolean> {
  try {
    // Get current state
    const state = await stateStore.getState(sessionId);
    if (!state) {
      throw new Error(`Session ${sessionId} not found`);
    }
    
    // Update current phase
    await stateStore.updateState(sessionId, {
      currentPhase: 'review',
      workflowProgress: {
        ...state.workflowProgress,
        currentPhase: 'review'
      }
    });
    
    // Get content draft artifact
    const artifacts = await artifactManager.getArtifacts(sessionId);
    const contentDraftArtifact = Object.values(artifacts).find(a => a.type === 'content-draft');
    
    if (!contentDraftArtifact) {
      throw new Error('Content draft artifact not found. Cannot proceed with review phase.');
    }
    
    // Get keyword analysis artifact
    const keywordAnalysisArtifact = Object.values(artifacts).find(a => a.type === 'keyword-analysis');
    
    // Extract keywords from keyword analysis if available
    let keywords = additionalParams.keywords || [];
    if (keywordAnalysisArtifact && keywordAnalysisArtifact.content) {
      try {
        if (keywordAnalysisArtifact.content.keywords && Array.isArray(keywordAnalysisArtifact.content.keywords)) {
          keywords = keywordAnalysisArtifact.content.keywords.map((k: any) => 
            typeof k === 'string' ? k : k.keyword || k.term || ''
          ).filter(Boolean);
        }
      } catch (error) {
        logger.warn('Error extracting keywords from keyword analysis', {
          sessionId,
          error: String(error)
        });
      }
    }
    
    // Create SEO optimization request message
    const seoOptimizationRequest = createEnhancedIterativeMessage(
      'system',
      AgentId.SEO_OPTIMIZATION,
      IterativeMessageType.INITIAL_REQUEST,
      {
        topic,
        contentType,
        targetAudience,
        tone,
        keywords,
        contentDraft: contentDraftArtifact.content,
        instructions: 'Optimize content for SEO while maintaining quality and readability'
      },
      {
        sessionId,
        conversationId: additionalParams.discussionId || sessionId,
        artifactReferences: [contentDraftArtifact.id],
        reasoning: {
          thoughts: ['Beginning review phase with SEO optimization'],
          considerations: [
            'Need to optimize content for search engines',
            'Should maintain readability and engagement',
            'Must ensure proper keyword usage and placement',
            'Should improve meta tags and structure'
          ],
          decision: 'Request SEO optimization of content draft',
          confidence: 0.9
        },
        metadata: {
          workflowPhase: 'review',
          goalId: 'seo-optimization-goal',
          contentDraftArtifactId: contentDraftArtifact.id,
          keywordAnalysisArtifactId: keywordAnalysisArtifact?.id
        }
      }
    );
    
    // Send SEO optimization request
    await enhancedMessageBus.sendMessage(sessionId, seoOptimizationRequest);
    
    // Update goal status
    await validationManager.updateGoalStatus(
      sessionId,
      'seo-optimization-goal',
      'in-progress',
      'SEO optimization requested'
    );
    
    logger.info(`Review phase initiated`, {
      sessionId,
      topic,
      contentType,
      targetAudience,
      tone,
      timestamp: new Date().toISOString()
    });
    
    // Schedule validation of review phase
    setTimeout(() => {
      validateEnhancedSeoOptimization(
        sessionId,
        topic,
        contentType,
        targetAudience,
        tone,
        additionalParams
      ).catch((err: Error) => {
        logger.error(`Error validating review phase`, {
          sessionId,
          phase: 'review-validation',
          error: err.message || String(err),
          stack: err.stack
        });
      });
    }, 60000); // Check after 1 minute
    
    return true;
  } catch (error) {
    const err = error as Error;
    logger.error(`Error initiating review phase`, {
      sessionId,
      phase: 'review',
      error: err.message || String(error),
      stack: err.stack
    });
    return false;
  }
}

/**
 * Validate the enhanced SEO optimization phase
 */
export async function validateEnhancedSeoOptimization(
  sessionId: string,
  topic: string,
  contentType: string,
  targetAudience: string,
  tone: string,
  additionalParams: Record<string, any> = {}
): Promise<boolean> {
  try {
    // Get current state
    const state = await stateStore.getState(sessionId);
    if (!state) {
      throw new Error(`Session ${sessionId} not found`);
    }
    
    // Skip if not in review phase
    if (state.currentPhase !== 'review') {
      logger.info(`Skipping review phase validation - current phase is ${state.currentPhase}`, {
        sessionId,
        currentPhase: state.currentPhase,
        timestamp: new Date().toISOString()
      });
      return false;
    }
    
    // Validate review phase
    const validationResult = await validationManager.validateReviewPhase(sessionId);
    
    if (validationResult.isValid) {
      logger.info(`Review phase validation passed`, {
        sessionId,
        timestamp: new Date().toISOString()
      });
      
      // Transition to completion
      return await finalizeWorkflow(
        sessionId,
        topic,
        contentType,
        targetAudience,
        tone,
        additionalParams
      );
    } else {
      logger.info(`Review phase validation failed`, {
        sessionId,
        details: validationResult.details,
        timestamp: new Date().toISOString()
      });
      
      // Check if SEO optimized content is missing
      if (validationResult.missingArtifacts.includes('seo-optimized-content')) {
        // If SEO optimized content is missing, request it again
        logger.info(`SEO optimized content missing, requesting again`, {
          sessionId,
          timestamp: new Date().toISOString()
        });
        
        // Re-request SEO optimization
        await initiateEnhancedReviewPhase(
          sessionId,
          topic,
          contentType,
          targetAudience,
          tone,
          additionalParams
        );
      } else if (validationResult.lowQualityArtifacts.includes('seo-optimized-content')) {
        // If SEO optimized content is low quality, request improvement
        logger.info(`SEO optimized content quality below threshold, requesting improvement`, {
          sessionId,
          timestamp: new Date().toISOString()
        });
        
        // Get SEO optimized content artifact
        const artifacts = await artifactManager.getArtifacts(sessionId);
        const seoOptimizedContentArtifact = Object.values(artifacts).find(a => a.type === 'seo-optimized-content');
        
        if (seoOptimizedContentArtifact) {
          // Request consultation from content strategy agent
          const consultationId = await consultationManager.requestConsultation(
            sessionId,
            'system',
            AgentId.CONTENT_STRATEGY,
            'How can this SEO-optimized content be improved to better align with the content strategy?',
            {
              seoOptimizedContent: seoOptimizedContentArtifact.content,
              qualityScore: seoOptimizedContentArtifact.qualityScore
            },
            seoOptimizedContentArtifact.id
          );
          
          logger.info(`Requested content strategy consultation for SEO content improvement`, {
            sessionId,
            consultationId,
            seoOptimizedContentId: seoOptimizedContentArtifact.id,
            timestamp: new Date().toISOString()
          });
          
          // Request SEO improvement
          const seoImprovementRequest = createEnhancedIterativeMessage(
            'system',
            AgentId.SEO_OPTIMIZATION,
            IterativeMessageType.ITERATION_REQUEST,
            {
              artifactId: seoOptimizedContentArtifact.id,
              currentVersion: seoOptimizedContentArtifact.currentVersion,
              instructions: 'Improve the SEO optimization based on feedback',
              consultationIds: [consultationId],
              requiredQualityScore: QUALITY_THRESHOLDS.SEO_OPTIMIZATION
            },
            {
              sessionId,
              conversationId: additionalParams.discussionId || sessionId,
              artifactId: seoOptimizedContentArtifact.id,
              version: seoOptimizedContentArtifact.currentVersion,
              reasoning: {
                thoughts: ['SEO optimized content needs improvement'],
                considerations: [
                  'Quality score is below threshold',
                  'Need to incorporate feedback from content strategy expert',
                  'Must maintain readability while improving SEO'
                ],
                decision: 'Request SEO optimization improvement with specific feedback',
                confidence: 0.9
              },
              metadata: {
                workflowPhase: 'review',
                goalId: 'seo-optimization-goal',
                isImprovement: true
              }
            }
          );
          
          // Send SEO improvement request
          await enhancedMessageBus.sendMessage(sessionId, seoImprovementRequest);
        }
      }
      
      // Schedule another validation check
      setTimeout(() => {
        validateEnhancedSeoOptimization(
          sessionId,
          topic,
          contentType,
          targetAudience,
          tone,
          additionalParams
        ).catch((err: Error) => {
          logger.error(`Error in scheduled review phase validation`, {
            sessionId,
            phase: 'review-validation',
            error: err.message || String(err),
            stack: err.stack
          });
        });
      }, 30000); // Check again after 30 seconds
      
      return false;
    }
  } catch (error) {
    const err = error as Error;
    logger.error(`Error validating review phase`, {
      sessionId,
      phase: 'review-validation',
      error: err.message || String(error),
      stack: err.stack
    });
    return false;
  }
}

/**
 * Finalize the workflow
 */
async function finalizeWorkflow(
  sessionId: string,
  topic: string,
  contentType: string,
  targetAudience: string,
  tone: string,
  additionalParams: Record<string, any> = {}
): Promise<boolean> {
  try {
    // Get current state
    const state = await stateStore.getState(sessionId);
    if (!state) {
      throw new Error(`Session ${sessionId} not found`);
    }
    
    // Get SEO optimized content artifact
    const artifacts = await artifactManager.getArtifacts(sessionId);
    const seoOptimizedContentArtifact = Object.values(artifacts).find(a => a.type === 'seo-optimized-content');
    
    if (!seoOptimizedContentArtifact) {
      throw new Error('SEO optimized content artifact not found. Cannot finalize workflow.');
    }
    
    // Create final output artifact
    const finalArtifactId = await artifactManager.createArtifact(
      sessionId,
      'final-content',
      `Final ${contentType} about ${topic}`,
      seoOptimizedContentArtifact.content,
      'system',
      {
        topic,
        contentType,
        targetAudience,
        tone,
        finalizedAt: new Date().toISOString(),
        sourceArtifactId: seoOptimizedContentArtifact.id
      },
      seoOptimizedContentArtifact.qualityScore
    );
    
    // Update artifact status
    await artifactManager.updateArtifactStatus(
      sessionId,
      finalArtifactId,
      ArtifactStatus.COMPLETED,
      'system',
      {
        thoughts: ['Workflow completed successfully'],
        considerations: [
          'All phases completed with quality thresholds met',
          'Final content incorporates market research, SEO, and content strategy',
          'Content has been optimized for search engines'
        ],
        decision: 'Finalize content and mark workflow as complete',
        confidence: 0.95
      }
    );
    
    // Update workflow status
    await stateStore.updateState(sessionId, {
      status: 'completed',
      endTime: new Date().toISOString(),
      currentPhase: 'completed',
      workflowProgress: {
        ...state.workflowProgress,
        marketResearchComplete: true,
        keywordResearchComplete: true,
        contentStrategyComplete: true,
        contentGenerationComplete: true,
        seoOptimizationComplete: true,
        currentPhase: 'completed'
      }
    });
    
    // Create completion message
    const completionMessage = createEnhancedIterativeMessage(
      'system',
      'all',
      IterativeMessageType.FINAL_OUTPUT,
      {
        event: 'WORKFLOW_COMPLETED',
        finalArtifactId,
        topic,
        contentType,
        targetAudience,
        tone,
        summary: 'Collaborative content creation workflow completed successfully.'
      },
      {
        sessionId,
        conversationId: additionalParams.discussionId || sessionId,
        artifactReferences: [finalArtifactId],
        reasoning: {
          thoughts: ['Workflow has been completed successfully'],
          considerations: [
            'All required artifacts have been created',
            'Quality thresholds have been met',
            'Final content is ready for use'
          ],
          decision: 'Mark workflow as completed and deliver final content',
          confidence: 0.95
        },
        metadata: {
          workflowPhase: 'completion',
          finalArtifactId
        }
      }
    );
    
    // Send completion message
    await enhancedMessageBus.sendMessage(sessionId, completionMessage);
    
    logger.info(`Workflow completed`, {
      sessionId,
      topic,
      contentType,
      finalArtifactId,
      timestamp: new Date().toISOString()
    });
    
    return true;
  } catch (error) {
    const err = error as Error;
    logger.error(`Error finalizing workflow`, {
      sessionId,
      error: err.message || String(error),
      stack: err.stack
    });
    return false;
  }
}
