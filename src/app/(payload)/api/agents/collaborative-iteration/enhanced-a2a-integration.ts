/**
 * Enhanced A2A Protocol Integration
 *
 * This file exports all components needed for the enhanced A2A protocol integration
 * with the iterative collaboration system.
 */

// Enhanced message types
import {
  createEnhancedIterativeMessage
} from './types/enhanced-message';
import type { EnhancedIterativeMessage } from './types/enhanced-message';

// Message adapter utilities
import {
  convertToEnhancedIterativeMessage,
  convertToIterativeMessage,
  convertA2AToEnhancedIterativeMessage
} from './utils/message-adapter';

// Enhanced message bus
import {
  enhancedMessageBus,
  EnhancedMessageBus
} from './utils/enhancedMessageBus';

// Consultation manager
import {
  consultationManager,
  ConsultationManager
} from './utils/consultation-manager';

// Artifact manager
import {
  artifactManager,
  ArtifactManager
} from './utils/artifactManager';

// Validation manager
import {
  validationManager,
  ValidationManager
} from './utils/validation-manager';
import type { ValidationCriteria, ValidationResult } from './utils/validation-manager';

// Enhanced workflow orchestrator
import {
  initiateEnhancedCollaborativeWorkflow,
  transitionToContentGeneration,
  transitionToReviewPhase,
  QUALITY_THRESHOLDS
} from './workflows/enhanced-workflow-orchestrator';

// Enhanced research phase
import {
  initiateEnhancedResearchPhase,
  validateEnhancedResearchPhase
} from './workflows/enhanced-research-phase';

// Enhanced content generation phase
import {
  initiateEnhancedContentGenerationPhase,
  validateEnhancedContentGeneration
} from './workflows/enhanced-content-generation';

// Enhanced review phase
import {
  initiateEnhancedReviewPhase,
  validateEnhancedSeoOptimization
} from './workflows/enhanced-review-phase';

// Export all components
export {
  // Enhanced message types
  createEnhancedIterativeMessage,

  // Message adapter utilities
  convertToEnhancedIterativeMessage,
  convertToIterativeMessage,
  convertA2AToEnhancedIterativeMessage,

  // Enhanced message bus
  enhancedMessageBus,
  EnhancedMessageBus,

  // Consultation manager
  consultationManager,
  ConsultationManager,

  // Artifact manager
  artifactManager,
  ArtifactManager,

  // Validation manager
  validationManager,
  ValidationManager,

  // Enhanced workflow orchestrator
  initiateEnhancedCollaborativeWorkflow,
  transitionToContentGeneration,
  transitionToReviewPhase,
  QUALITY_THRESHOLDS,

  // Enhanced research phase
  initiateEnhancedResearchPhase,
  validateEnhancedResearchPhase,

  // Enhanced content generation phase
  initiateEnhancedContentGenerationPhase,
  validateEnhancedContentGeneration,

  // Enhanced review phase
  initiateEnhancedReviewPhase,
  validateEnhancedSeoOptimization
};

// Export types separately
export type {
  EnhancedIterativeMessage,
  ValidationCriteria,
  ValidationResult
};

/**
 * Initialize the enhanced A2A integration
 * This function should be called at application startup
 */
export async function initializeEnhancedA2AIntegration(): Promise<void> {
  console.log('Initializing Enhanced A2A Protocol Integration');

  // Any initialization logic can go here

  console.log('Enhanced A2A Protocol Integration initialized successfully');
}
