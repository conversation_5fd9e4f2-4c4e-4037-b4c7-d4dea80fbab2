/**
 * Controller Adapter for Enhanced Message Bus
 * 
 * This adapter provides a simplified interface for working with the collaborative agent system
 * using the enhanced message bus and state store.
 */

import { v4 as uuidv4 } from 'uuid';
import { stateStore } from './stateStore';
import { enhancedMessageBus } from './enhancedMessageBus';
import { 
  IterativeCollaborationState, 
  IterativeMessage, 
  AgentId,
  IterativeMessageType
} from '../types';
import { getAgentRoleType } from './agentRoleMapper';
import logger from './logger';

/**
 * Controller adapter that works with the enhanced message bus and state store
 * Temporarily simplified to fix build issues
 */
export class CollaborationControllerAdapter {
  /**
   * Start a new collaborative session with improved error handling and state verification
   */
  async startSession(
    topic: string,
    contentType: string,
    targetAudience: string,
    tone: string = 'professional',
    keywords: string[] = [],
    additionalInstructions: string = '',
    clientName: string = ''
  ): Promise<{ sessionId: string; state: IterativeCollaborationState }> {
    try {
      // Generate a unique session ID
      const sessionId = uuidv4();
      
      logger.info(`Starting new collaboration session ${sessionId}`, { 
        sessionId, 
        topic, 
        contentType
      });
      
      // Create initial state with all required fields
      const state: IterativeCollaborationState = {
        id: sessionId,
        topic,
        contentType: contentType as 'blog-article' | 'product-page' | 'buying-guide', // Cast to expected enum types
        targetAudience,
        tone,
        keywords,
        status: 'active',
        startTime: new Date().toISOString(),
        artifacts: {},
        consultations: {},
        agentStates: {},
        currentPhase: 'planning',
        collaborationState: 'planning',
        messages: [],
        iterations: 0,
        maxIterations: 5,
        events: [],
        sessionId,
        // Create a proper Discussion record with ID as the key
        discussions: (() => {
          const discussionId = uuidv4();
          return {
            [discussionId]: { 
              id: discussionId, 
              topic, 
              status: 'active',
              participants: Object.values(AgentId),
              leadAgent: AgentId.CONTENT_STRATEGY,
              messages: [],
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString()
            }
          };
        })()
      };
      
      // Add client name and additional instructions if provided
      if (clientName) {
        state.clientName = clientName;
      }
      
      if (additionalInstructions) {
        state.additionalInstructions = additionalInstructions;
      }
      
      // Initialize agent states
      const agents = Object.values(AgentId);
      
      // Create agent tracking entries
      state.agents = {};
      
      agents.forEach(agentId => {
        // Create agent state record
        state.agentStates[agentId] = {
          id: agentId,
          processedRequests: [],
          generatedArtifacts: [],
          consultationsProvided: [],
          consultationsReceived: [],
          lastUpdated: new Date().toISOString()
        };
        
        // Track active agents
        if (state.agents) {
          state.agents[agentId] = {
            active: true,
            role: getAgentRoleType(agentId),
            status: 'ready'
          };
        }
      });
      
      // Save the state in the Redis state store
      logger.info(`Saving initial state to Redis for session ${sessionId}`);
      await stateStore.setState(sessionId, state);

      // CRITICAL: Verify the state was saved successfully by retrieving it
      const verifiedState = await stateStore.getState(sessionId);
      if (!verifiedState) {
        logger.error(`State verification failed for session ${sessionId}`, { sessionId });
        throw new Error('Failed to persist session state to Redis');
      }

      logger.info(`State verification succeeded for session ${sessionId}`, {
        sessionId,
        stateExists: Boolean(verifiedState),
        artifactsCount: Object.keys(verifiedState.artifacts || {}).length,
        messagesCount: (verifiedState.messages || []).length
      });
      
      // Create the initial system message using the session-specific message bus
      try {
        const sessionBus = enhancedMessageBus.createSessionBus(sessionId);

        // Create and send the initial system message
        const initMessage = sessionBus.createBroadcastMessage(
          'system',
          IterativeMessageType.SYSTEM_MESSAGE,
          {
            event: 'SESSION_CREATED',
            details: {
              topic,
              contentType,
              targetAudience,
              tone,
              keywords: keywords.join(', ')
            }
          }
        );

        await sessionBus.sendMessage(initMessage);

        logger.debug(`Initialization message sent to all agents`, {
          sessionId,
          messageId: initMessage.id
        });
      } catch (err) {
        logger.error(`Error initializing session message bus`, {
          sessionId,
          error: err instanceof Error ? err.message : String(err)
        });
      }
      
      return { sessionId, state };
    } catch (error) {
      logger.error('Failed to start collaboration session', {
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined
      });
      throw error;
    }
  }
  
  /**
   * Get a session by ID
   */
  async getSession(sessionId: string): Promise<IterativeCollaborationState | null> {
    try {
      return await stateStore.getState(sessionId);
    } catch (error) {
      logger.error(`Error retrieving session ${sessionId}`, {
        sessionId,
        error: error instanceof Error ? error.message : String(error)
      });
      return null;
    }
  }
  
  /**
   * Send a message within a session
   */
  async sendMessage(
    sessionId: string, 
    message: IterativeMessage
  ): Promise<{ success: boolean; error?: string }> {
    try {
      const session = await this.getSession(sessionId);
      
      if (!session) {
        return { 
          success: false, 
          error: `Session ${sessionId} not found` 
        };
      }
      
      // Ensure the message has the correct sessionId
      message.sessionId = sessionId;
      message.conversationId = sessionId;
      
      // Get the session-specific message bus
      const sessionBus = enhancedMessageBus.createSessionBus(sessionId);

      // Send the message
      const result = await sessionBus.sendMessage(message);
      
      if (result.error) {
        return {
          success: false,
          error: result.error
        };
      }
      
      return { success: true };
    } catch (error) {
      logger.error(`Error sending message in session ${sessionId}`, {
        sessionId,
        messageId: message.id,
        error: error instanceof Error ? error.message : String(error)
      });
      
      return { 
        success: false, 
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }
  
  /**
   * Get all message history for a session
   */
  async getMessageHistory(sessionId: string): Promise<IterativeMessage[]> {
    try {
      const session = await this.getSession(sessionId);
      
      if (!session || !session.messages) {
        return [];
      }
      
      return session.messages;
    } catch (error) {
      logger.error(`Error retrieving message history for session ${sessionId}`, {
        sessionId,
        error: error instanceof Error ? error.message : String(error)
      });
      return [];
    }
  }
  
  /**
   * Update a session with new state
   */
  async updateSession(
    sessionId: string, 
    updates: Partial<IterativeCollaborationState>
  ): Promise<boolean> {
    try {
      const session = await this.getSession(sessionId);
      
      if (!session) {
        return false;
      }
      
      // Create updated state
      const updatedState: IterativeCollaborationState = {
        ...session,
        ...updates,
        // Ensure these fields are not overwritten
        id: session.id,
        sessionId: session.sessionId || sessionId
      };
      
      // Save updated state
      await stateStore.setState(sessionId, updatedState);
      
      return true;
    } catch (error) {
      logger.error(`Error updating session ${sessionId}`, {
        sessionId,
        error: error instanceof Error ? error.message : String(error)
      });
      return false;
    }
  }
}

// Export a singleton instance
export const collaborationController = new CollaborationControllerAdapter();
