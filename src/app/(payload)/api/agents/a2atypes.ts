/**
 * A2A Protocol Types
 * 
 * Core type definitions for the Agent-to-Agent communication protocol
 */

/**
 * Basic message part types
 */
export interface Part {
  type: 'text' | 'data';
  text?: string;
  data?: any;
}

export interface TextPart extends Part {
  type: 'text';
  text: string;
}

export interface DataPart extends Part {
  type: 'data';
  data: any;
}

/**
 * Core A2A Message structure
 */
export interface A2AMessage {
  role: 'system' | 'agent' | 'user';
  parts: Part[];
}

/**
 * Reasoning structure for enhanced messages
 */
export interface Reasoning {
  thoughts: string[];
  considerations: string[];
  alternatives: string[];
  decision: string;
  confidence: number;
}

/**
 * Message intentions for enhanced communication
 */
export type MessageIntention = 
  | 'inform'
  | 'request'
  | 'suggest'
  | 'question'
  | 'feedback'
  | 'collaborate'
  | 'consensus';

/**
 * Requested actions for enhanced messages
 */
export interface RequestedAction {
  type: string;
  description: string;
  priority: 'low' | 'medium' | 'high';
  deadline?: string;
}

/**
 * Enhanced A2A Message with additional metadata
 */
export interface EnhancedA2AMessage extends A2AMessage {
  id: string;
  timestamp: string;
  from: string;
  to: string | string[];
  conversationId: string;
  replyTo?: string;
  reasoning?: Reasoning;
  intentions?: MessageIntention[];
  requestedActions?: RequestedAction[];
  artifactReferences?: string[];
  decisionReferences?: string[];
  metadata?: Record<string, any>;
}

/**
 * Collaboration state for tracking agent interactions
 */
export interface CollaborationState {
  sessionId: string;
  participants: string[];
  status: 'active' | 'paused' | 'completed' | 'failed';
  startTime: string;
  lastActivity: string;
  artifacts: Record<string, any>;
  decisions: any[];
  metadata?: Record<string, any>;
}

/**
 * Artifact types for content generation
 */
export enum ArtifactType {
  MARKET_RESEARCH = 'market-research',
  KEYWORD_ANALYSIS = 'keyword-analysis',
  CONTENT_STRATEGY = 'content-strategy',
  CONTENT_DRAFT = 'content-draft',
  SEO_OPTIMIZATION = 'seo-optimization',
  FINAL_CONTENT = 'final-content'
}

/**
 * Agent capabilities
 */
export interface AgentCapability {
  id: string;
  name: string;
  description: string;
  inputTypes: string[];
  outputTypes: string[];
}

/**
 * Agent metadata
 */
export interface AgentMetadata {
  id: string;
  name: string;
  description: string;
  capabilities: AgentCapability[];
  version: string;
  status: 'active' | 'inactive' | 'maintenance';
}
