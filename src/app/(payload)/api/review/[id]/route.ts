import { NextRequest, NextResponse } from 'next/server';
import { getWorkflowEngine, getReviewSystem } from '@/core/workflow/singleton';

/**
 * GET /api/review/[id]
 * Returns review data for a specific review ID
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const reviewId = params.id;

    if (!reviewId) {
      return NextResponse.json(
        {
          success: false,
          error: 'Review ID is required'
        },
        { status: 400 }
      );
    }

    // Get review system and retrieve actual review data
    const reviewSystem = getReviewSystem();
    const review = await reviewSystem.getReview(reviewId);

    if (!review) {
      return NextResponse.json(
        {
          success: false,
          error: 'Review not found'
        },
        { status: 404 }
      );
    }

    // Format review data for API response
    const reviewData = {
      id: review.id,
      executionId: review.executionId,
      stepId: review.stepId,
      type: review.type,
      status: review.status,
      createdAt: review.createdAt,
      deadline: review.deadline,
      content: review.content,
      instructions: review.instructions,
      reviewer: review.reviewer,
      metadata: review.metadata || {}
    };

    return NextResponse.json({
      success: true,
      data: reviewData
    });
  } catch (error) {
    console.error('Error getting review data:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to get review data' 
      },
      { status: 500 }
    );
  }
}

/**
 * POST /api/review/[id]
 * Submits review decision
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const reviewId = params.id;
    const body = await request.json();
    const { decision, edits, feedback } = body;

    if (!reviewId) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Review ID is required' 
        },
        { status: 400 }
      );
    }

    if (!decision || !['approve', 'reject'].includes(decision)) {
      return NextResponse.json(
        {
          success: false,
          error: 'Valid decision (approve/reject) is required'
        },
        { status: 400 }
      );
    }

    // Get review system and workflow engine
    const reviewSystem = getReviewSystem();
    const workflowEngine = getWorkflowEngine();

    console.log('Processing review decision:', {
      reviewId,
      decision,
      edits,
      feedback
    });

    // Get review to find execution and step info
    const review = await reviewSystem.getReview(reviewId);
    if (!review) {
      return NextResponse.json(
        {
          success: false,
          error: 'Review not found'
        },
        { status: 404 }
      );
    }

    // Submit review decision to review system
    await reviewSystem.submitReview(reviewId, decision, edits || feedback);

    // **CRITICAL**: Notify workflow engine to handle review completion
    if (review.executionId && review.stepId) {
      await workflowEngine.handleReviewCompletion(
        review.executionId,
        review.stepId,
        {
          decision,
          feedback: edits || feedback,
          reviewer: 'user'
        }
      );
    }

    const reviewResult = {
      reviewId,
      decision,
      edits: edits || '',
      feedback: feedback || '',
      submittedAt: new Date().toISOString(),
      processed: true
    };

    return NextResponse.json({
      success: true,
      data: reviewResult,
      message: `Review ${decision}d successfully. ${decision === 'approve' ? 'Workflow continuing...' : 'Processing feedback and regenerating content...'}`
    });
  } catch (error) {
    console.error('Error processing review decision:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to process review decision' 
      },
      { status: 500 }
    );
  }
}
