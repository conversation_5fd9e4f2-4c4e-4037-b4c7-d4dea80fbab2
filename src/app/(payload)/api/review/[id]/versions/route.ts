import { NextRequest, NextResponse } from 'next/server';

/**
 * GET /api/review/[id]/versions
 * Returns version history for a review
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const reviewId = params.id;

    if (!reviewId) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Review ID is required' 
        },
        { status: 400 }
      );
    }

    // TODO: Retrieve version history from database/state store
    // For now, we'll simulate version data
    const versions = [
      {
        id: 'v1',
        reviewId,
        version: 1,
        createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(), // 2 hours ago
        status: 'rejected',
        feedback: 'Please add more specific examples and improve the introduction.',
        reviewer: 'content-manager',
        changes: [
          'Added introduction section',
          'Included 3 case studies',
          'Improved SEO meta description'
        ]
      },
      {
        id: 'v2',
        reviewId,
        version: 2,
        createdAt: new Date(Date.now() - 1 * 60 * 60 * 1000).toISOString(), // 1 hour ago
        status: 'pending',
        feedback: null,
        reviewer: 'content-team',
        changes: [
          'Addressed previous feedback',
          'Added specific business examples',
          'Enhanced introduction with compelling hook',
          'Optimized for target keywords'
        ]
      }
    ];

    return NextResponse.json({
      success: true,
      data: {
        reviewId,
        versions,
        currentVersion: 2,
        totalVersions: versions.length
      }
    });
  } catch (error) {
    console.error('Error getting review versions:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to get review versions' 
      },
      { status: 500 }
    );
  }
}
