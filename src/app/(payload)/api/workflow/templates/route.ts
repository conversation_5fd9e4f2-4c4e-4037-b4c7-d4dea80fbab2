import { NextRequest, NextResponse } from 'next/server';
import { ESSENTIAL_TEMPLATES } from '@/core/workflow/templates';

/**
 * GET /api/workflow/templates
 * Returns available workflow templates
 */
export async function GET(request: NextRequest) {
  try {
    // Convert the full template registry to API format
    const templates = ESSENTIAL_TEMPLATES.map(template => ({
      id: template.id,
      name: template.name,
      description: template.description,
      category: template.category,
      tags: template.tags,
      difficulty: template.difficulty,
      estimatedTime: template.estimatedTime,
      consultationEnabled: template.consultationEnabled,
      agentCount: template.agentCount,
      featured: template.featured,
      steps: template.workflow.steps.map(step => ({
        id: step.id,
        name: step.name,
        type: step.type
      }))
    }));

    return NextResponse.json({
      success: true,
      data: {
        templates
      }
    });
  } catch (error) {
    console.error('Error loading templates:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to load templates' 
      },
      { status: 500 }
    );
  }
}
