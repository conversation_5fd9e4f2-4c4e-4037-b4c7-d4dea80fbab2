import { NextRequest, NextResponse } from 'next/server';
import { getWorkflowEngine } from '@/core/workflow/singleton';

/**
 * GET /api/workflow/execution/[id]
 * Returns execution status and details
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: executionId } = await params;

    if (!executionId) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Execution ID is required' 
        },
        { status: 400 }
      );
    }

    // Get workflow engine and retrieve actual execution
    const engine = getWorkflowEngine();
    const execution = await engine.getExecution(executionId);

    if (!execution) {
      return NextResponse.json(
        {
          success: false,
          error: 'Execution not found'
        },
        { status: 404 }
      );
    }

    // Get workflow details
    const workflow = await engine.getWorkflow(execution.workflowId);

    // Format execution data for API response
    const formattedExecution = {
      id: execution.id,
      templateId: execution.workflowId,
      status: execution.status,
      createdAt: execution.startedAt,
      updatedAt: execution.updatedAt || execution.startedAt,
      currentStep: execution.currentStep,
      progress: execution.progress,
      steps: workflow?.steps.map(step => {
        const stepResult = execution.stepResults[step.id];
        if (stepResult) {
          return {
            id: step.id,
            name: step.name,
            status: stepResult.status,
            startedAt: stepResult.startedAt,
            completedAt: stepResult.completedAt,
            consultationEnabled: !!step.consultationConfig?.enabled
          };
        } else {
          return {
            id: step.id,
            name: step.name,
            status: 'pending',
            consultationEnabled: !!step.consultationConfig?.enabled
          };
        }
      }) || [],
      artifacts: execution.outputs,
      metadata: {
        estimatedCompletion: execution.estimatedCompletion,
        agentsInvolved: workflow?.steps
          .filter(step => step.consultationConfig?.enabled)
          .flatMap(step => step.consultationConfig?.triggers.flatMap(t => t.agents) || []) || [],
        consultationEnabled: workflow?.steps.some(step => step.consultationConfig?.enabled) || false
      }
    };

    return NextResponse.json({
      success: true,
      data: formattedExecution
    });
  } catch (error) {
    console.error('Error getting execution status:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to get execution status' 
      },
      { status: 500 }
    );
  }
}
