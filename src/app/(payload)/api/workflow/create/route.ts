import { NextRequest, NextResponse } from 'next/server';
import { getWorkflowEngine, getEnhancedTemplateRegistry } from '@/core/workflow/singleton';
import { v4 as uuidv4 } from 'uuid';

/**
 * POST /api/workflow/create
 * Creates and executes a new workflow
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { templateId, inputs, userId } = body;

    if (!templateId || !inputs) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'templateId and inputs are required' 
        },
        { status: 400 }
      );
    }

    // Get workflow engine and template registry
    const engine = getWorkflowEngine();
    const registry = getEnhancedTemplateRegistry();

    // Process template to create workflow
    const processedTemplate = registry.processTemplate(templateId, userId);
    if (!processedTemplate) {
      return NextResponse.json(
        {
          success: false,
          error: `Template ${templateId} not found`
        },
        { status: 404 }
      );
    }

    // The processed template already has a workflow with an ID
    // Store the workflow directly in the state store to preserve the ID
    const workflow = processedTemplate.workflow;

    // Check if workflow already exists, if not store it
    const existingWorkflow = await engine.getWorkflow(workflow.id);
    if (!existingWorkflow) {
      // Store the workflow with its existing ID by using the state store directly
      const stateStore = engine.getStateStore();
      await stateStore.setWorkflow(workflow);
      console.log(`🔄 Stored template workflow: ${workflow.id} (${workflow.name})`);
    }

    // Execute the workflow using the template's workflow ID
    const executionId = await engine.executeWorkflow(
      workflow.id,
      inputs,
      { userId: userId || 'anonymous' }
    );

    console.log('Created workflow execution:', executionId);

    return NextResponse.json({
      success: true,
      data: {
        executionId,
        message: 'Workflow created and started successfully'
      }
    });
  } catch (error) {
    console.error('Error creating workflow:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to create workflow' 
      },
      { status: 500 }
    );
  }
}

/**
 * GET /api/workflow/create
 * Returns available templates (for backward compatibility)
 */
export async function GET(request: NextRequest) {
  // Redirect to templates endpoint
  const url = new URL('/api/workflow/templates', request.url);
  return NextResponse.redirect(url);
}
