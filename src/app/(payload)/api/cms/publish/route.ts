import { NextRequest, NextResponse } from 'next/server';
import configPromise from '@payload-config';
import { getPayload } from 'payload';

/**
 * POST /api/cms/publish
 * Publishes content to the CMS
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { artifactId, artifact, collection = 'posts' } = body;

    if (!artifactId || !artifact) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'artifactId and artifact are required' 
        },
        { status: 400 }
      );
    }

    // Get Payload CMS instance
    const payload = await getPayload({
      config: configPromise,
    });

    // Prepare content data based on artifact type
    let contentData;
    
    switch (artifact.type) {
      case 'blog-article':
        contentData = {
          title: artifact.title || 'Untitled Article',
          content: artifact.content || '',
          status: 'draft',
          meta: {
            description: artifact.metadata?.metaDescription || '',
            keywords: artifact.metadata?.keywords || [],
            seoScore: artifact.metadata?.seoScore || 0
          },
          publishedAt: new Date().toISOString(),
          author: 'AI Content Generator',
          category: artifact.metadata?.category || 'general',
          tags: artifact.metadata?.tags || []
        };
        break;
        
      case 'product-page':
        contentData = {
          title: artifact.title || 'Untitled Product',
          content: artifact.content || '',
          status: 'draft',
          productInfo: {
            features: artifact.metadata?.features || [],
            benefits: artifact.metadata?.benefits || [],
            specifications: artifact.metadata?.specifications || {}
          },
          meta: {
            description: artifact.metadata?.metaDescription || '',
            keywords: artifact.metadata?.keywords || []
          },
          publishedAt: new Date().toISOString()
        };
        break;
        
      default:
        contentData = {
          title: artifact.title || 'Untitled Content',
          content: artifact.content || '',
          status: 'draft',
          type: artifact.type,
          metadata: artifact.metadata || {},
          publishedAt: new Date().toISOString()
        };
    }

    try {
      // Create the content in Payload CMS
      const result = await payload.create({
        collection,
        data: contentData
      });

      console.log('Content published to CMS:', result.id);

      return NextResponse.json({
        success: true,
        data: {
          id: result.id,
          collection,
          artifactId,
          publishedAt: new Date().toISOString(),
          url: `/admin/collections/${collection}/${result.id}`,
          status: 'published'
        },
        message: 'Content published successfully to CMS'
      });
    } catch (cmsError) {
      console.error('Error publishing to CMS:', cmsError);
      
      // Fallback: simulate successful publication
      const simulatedId = `cms-${Date.now()}`;
      
      return NextResponse.json({
        success: true,
        data: {
          id: simulatedId,
          collection,
          artifactId,
          publishedAt: new Date().toISOString(),
          url: `/admin/collections/${collection}/${simulatedId}`,
          status: 'published',
          note: 'Simulated publication (CMS integration pending)'
        },
        message: 'Content prepared for publication (CMS integration pending)'
      });
    }
  } catch (error) {
    console.error('Error in CMS publish endpoint:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to publish content to CMS' 
      },
      { status: 500 }
    );
  }
}
