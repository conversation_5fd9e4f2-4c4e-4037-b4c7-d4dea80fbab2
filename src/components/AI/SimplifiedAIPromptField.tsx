'use client'
import React, { useState, useEffect, useCallback } from 'react'

// Interface for saved prompts
interface SavedPrompt {
  id: string;
  name: string;
  fieldName: string;
  collectionSlug: string;
  prompt: string;
  createdAt: string;
  updatedAt: string;
}

// UI field component for AI content generation
export const AIHelperField = (props) => {
  const { path, field } = props
  
  const [promptText, setPromptText] = useState('')
  const [promptName, setPromptName] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState('')
  const [isExpanded, setIsExpanded] = useState(false)
  const [showPromptList, setShowPromptList] = useState(false)
  const [targetField, setTargetField] = useState('')
  const [collectionSlug, setCollectionSlug] = useState('')
  const [savedPrompts, setSavedPrompts] = useState<SavedPrompt[]>([])
  const [selectedPromptId, setSelectedPromptId] = useState<string | null>(null)
  const [isCreatingNew, setIsCreatingNew] = useState(false)
  const [generatedContent, setGeneratedContent] = useState('')
  const [showGeneratedContent, setShowGeneratedContent] = useState(false)

  // Generate a unique ID
  const generateId = () => {
    return Date.now().toString(36) + Math.random().toString(36).substring(2);
  }

  // Extract collection slug from URL
  const getCollectionFromURL = useCallback(() => {
    try {
      const pathParts = window.location.pathname.split('/')
      // URL pattern is typically /admin/collections/{collectionSlug}/{id}
      const collectionIndex = pathParts.indexOf('collections')
      if (collectionIndex !== -1 && pathParts.length > collectionIndex + 1) {
        return pathParts[collectionIndex + 1]
      }
      return ''
    } catch (err) {
      console.error('Error extracting collection:', err)
      return ''
    }
  }, [])

  // Find the target field based on component props
  const determineTargetField = useCallback(() => {
    // First check if targetFieldName is explicitly provided in field admin options
    if (field.admin?.targetFieldName) {
      return field.admin.targetFieldName;
    }
    
    // Try to derive from the field name (remove "AIHelper" suffix)
    const fieldName = field.name || '';
    const targetName = fieldName.replace('AIHelper', '')
    
    if (targetName && targetName !== fieldName) {
      return targetName
    }
    
    // Fallback: try to find by DOM if name-based approach fails
    try {
      // Try to find a sibling field
      const possibleTargetField = path.replace('AIHelper', '')
      if (possibleTargetField !== path) {
        return possibleTargetField
      }
      
      return ''
    } catch (err) {
      console.error('Error finding target field:', err)
      return ''
    }
  }, [field, path])

  // Load saved prompts for this field and collection
  const loadSavedPrompts = useCallback(() => {
    if (!targetField || !collectionSlug) return []
    
    try {
      const storageKey = `ai-prompts-${collectionSlug}-${targetField}`
      const savedPromptsJson = localStorage.getItem(storageKey)
      
      if (savedPromptsJson) {
        const prompts = JSON.parse(savedPromptsJson) as SavedPrompt[]
        setSavedPrompts(prompts)
        
        // Select the most recently updated prompt by default
        if (prompts.length > 0) {
          const sortedPrompts = [...prompts].sort((a, b) => 
            new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime()
          )
          
          setSelectedPromptId(sortedPrompts[0].id)
          setPromptText(sortedPrompts[0].prompt)
          setPromptName(sortedPrompts[0].name)
        } else {
          createDefaultPrompt()
        }
        
        return prompts
      } else {
        // If no prompts exist yet, create a default one
        createDefaultPrompt()
        return []
      }
    } catch (err) {
      console.error('Error loading saved prompts:', err)
      createDefaultPrompt()
      return []
    }
  }, [targetField, collectionSlug, createDefaultPrompt])

  // Create a default prompt
  const createDefaultPrompt = useCallback(() => {
    if (!targetField || !collectionSlug) return
    
    const defaultPrompt = {
      id: generateId(),
      name: 'Default Prompt',
      fieldName: targetField,
      collectionSlug,
      prompt: `Generate high-quality content for the "${targetField}" field in the ${collectionSlug} collection.
      
The content should be professional, well-structured, and engaging. Include relevant details and use appropriate terminology.
      
Format the content with clear paragraphs and proper punctuation.`,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
    
    setSelectedPromptId(defaultPrompt.id)
    setPromptText(defaultPrompt.prompt)
    setPromptName(defaultPrompt.name)
    setSavedPrompts([defaultPrompt])
    
    // Save to localStorage
    const storageKey = `ai-prompts-${collectionSlug}-${targetField}`
    localStorage.setItem(storageKey, JSON.stringify([defaultPrompt]))
    
    return defaultPrompt
  }, [targetField, collectionSlug])

  // Initialize component
  useEffect(() => {
    const collection = getCollectionFromURL()
    setCollectionSlug(collection)
    
    const field = determineTargetField()
    setTargetField(field)
    
    if (field && collection) {
      loadSavedPrompts()
    }
  }, [getCollectionFromURL, determineTargetField, loadSavedPrompts])

  // Save prompt to localStorage
  const savePrompt = () => {
    if (!targetField || !collectionSlug) return false
    
    try {
      const now = new Date().toISOString()
      const storageKey = `ai-prompts-${collectionSlug}-${targetField}`
      
      if (isCreatingNew) {
        // Create a new prompt
        const newPrompt: SavedPrompt = {
          id: generateId(),
          name: promptName || `Prompt ${savedPrompts.length + 1}`,
          fieldName: targetField,
          collectionSlug,
          prompt: promptText,
          createdAt: now,
          updatedAt: now
        }
        
        const updatedPrompts = [...savedPrompts, newPrompt]
        setSavedPrompts(updatedPrompts)
        setSelectedPromptId(newPrompt.id)
        localStorage.setItem(storageKey, JSON.stringify(updatedPrompts))
        setIsCreatingNew(false)
      } else if (selectedPromptId) {
        // Update existing prompt
        const updatedPrompts = savedPrompts.map(p => {
          if (p.id === selectedPromptId) {
            return {
              ...p,
              name: promptName,
              prompt: promptText,
              updatedAt: now
            }
          }
          return p
        })
        
        setSavedPrompts(updatedPrompts)
        localStorage.setItem(storageKey, JSON.stringify(updatedPrompts))
      }
      
      // Show success message
      showTemporaryMessage('Prompt saved!', 'text-green-500')
      
      return true
    } catch (err) {
      console.error('Error saving prompt:', err)
      setError('Failed to save prompt')
      return false
    }
  }

  // Delete a prompt
  const deletePrompt = (promptId: string, e: React.MouseEvent) => {
    e.stopPropagation();
    
    if (!targetField || !collectionSlug) return
    if (savedPrompts.length <= 1) {
      showTemporaryMessage('Cannot delete the only prompt', 'text-yellow-500')
      return
    }
    
    try {
      const storageKey = `ai-prompts-${collectionSlug}-${targetField}`
      const filteredPrompts = savedPrompts.filter(p => p.id !== promptId)
      
      // If we're deleting the selected prompt, select another one
      if (promptId === selectedPromptId && filteredPrompts.length > 0) {
        setSelectedPromptId(filteredPrompts[0].id)
        setPromptText(filteredPrompts[0].prompt)
        setPromptName(filteredPrompts[0].name)
      }
      
      setSavedPrompts(filteredPrompts)
      localStorage.setItem(storageKey, JSON.stringify(filteredPrompts))
      
      showTemporaryMessage('Prompt deleted', 'text-green-500')
    } catch (err) {
      console.error('Error deleting prompt:', err)
      setError('Failed to delete prompt')
    }
  }

  // Start creating a new prompt
  const startNewPrompt = () => {
    setIsCreatingNew(true)
    setSelectedPromptId(null)
    setPromptName('')
    setPromptText('')
  }
  
  // Select a prompt
  const selectPrompt = (promptId: string) => {
    const prompt = savedPrompts.find(p => p.id === promptId)
    if (prompt) {
      setSelectedPromptId(promptId)
      setPromptText(prompt.prompt)
      setPromptName(prompt.name)
      setIsCreatingNew(false)
    }
  }

  // Utility function to show a temporary message
  const showTemporaryMessage = (message: string, className: string) => {
    // Remove any existing messages
    const existingMsg = document.querySelector('.temp-message')
    if (existingMsg) existingMsg.remove()
    
    // Create new message
    const msgElement = document.createElement('div')
    msgElement.textContent = message
    msgElement.className = `temp-message text-xs mt-1 ${className}`
    
    // Find container and append
    const container = document.querySelector(`[id$="${path}-field"]`)
    if (container) {
      container.appendChild(msgElement)
      
      // Remove after 2 seconds
      setTimeout(() => {
        msgElement.remove()
      }, 2000)
    }
  }

  // Improved function to find the target field in DOM
  const findTargetField = (): HTMLTextAreaElement | HTMLInputElement | null => {
    // First, try direct ID lookup (most reliable)
    let targetEl = document.getElementById(`field-${targetField}`) as HTMLTextAreaElement | HTMLInputElement | null;
    
    // Second, try different ID format that Payload might use
    if (!targetEl) {
      targetEl = document.getElementById(`${targetField}-field`) as HTMLTextAreaElement | HTMLInputElement | null;
    }
    
    // Third, try with dot notation for nested fields
    if (!targetEl && targetField.includes('.')) {
      const fieldPath = targetField.split('.').join('-');
      targetEl = document.getElementById(`field-${fieldPath}`) as HTMLTextAreaElement | HTMLInputElement | null;
    }
    
    // Last resort: query by name attribute
    if (!targetEl) {
      targetEl = document.querySelector(`[name="${targetField}"]`) as HTMLTextAreaElement | HTMLInputElement | null;
    }
    
    return targetEl;
  }

  // Apply generated content to the target field
  const applyGeneratedContent = () => {
    if (!targetField || !generatedContent) return;
    
    try {
      const targetInputEl = findTargetField();
      
      if (!targetInputEl) {
        throw new Error('Target field element not found');
      }
      
      // Update the field value
      targetInputEl.value = generatedContent;
      
      // Trigger input event to ensure Payload form state updates
      const event = new Event('input', { bubbles: true });
      targetInputEl.dispatchEvent(event);
      
      // Hide generated content preview and reset
      setShowGeneratedContent(false);
      setGeneratedContent('');
      
      showTemporaryMessage('Content applied successfully!', 'text-green-500');
    } catch (err) {
      console.error('Error applying content:', err);
      setError(`Error: ${err instanceof Error ? err.message : 'Failed to apply content'}`);
    }
  }

  // Generate content using the AI endpoint
  const generateContent = async () => {
    if (!targetField) {
      setError('Could not determine target field')
      return
    }
    
    setIsLoading(true)
    setError('')
    
    try {
      // Find the target input/textarea element with improved method
      const targetInputEl = findTargetField();
      
      if (!targetInputEl) {
        throw new Error('Target field element not found')
      }
      
      // Get the current field value
      const currentValue = targetInputEl.value || ''
      
      // Make API request to content generation endpoint
      const response = await fetch('/api/generate-ai-content', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          fieldName: targetField,
          collectionSlug,
          currentValue,
          userPrompt: promptText,
          fieldType: targetInputEl.tagName.toLowerCase() === 'textarea' ? 'textarea' : 'input'
        }),
      });
      
      if (!response.ok) {
        throw new Error('Failed to generate content')
      }
      
      const data = await response.json()
      
      // Show the content for review and editing
      if (data.generatedContent) {
        setGeneratedContent(data.generatedContent);
        setShowGeneratedContent(true);
        showTemporaryMessage('Content generated! You can edit it before applying.', 'text-green-500');
      } else {
        throw new Error('No content generated')
      }
    } catch (err) {
      console.error('Error generating content:', err)
      setError(`Error: ${err instanceof Error ? err.message : 'Failed to generate content'}`)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="field-type" style={{width: '100%'}}>
      {/* Main control buttons - styled to match Payload admin */}
      <div style={{display: 'flex', gap: '10px', marginBottom: '15px', width: '100%'}}>
        <button
          type="button"
          className="btn btn--style-primary"
          onClick={generateContent}
          disabled={isLoading}
          style={{flex: 2}}
        >
          {isLoading ? 'Generating...' : `Generate for "${targetField || 'field'}"`}
        </button>
        
        <div style={{display: 'flex', gap: '10px'}}>
          <button
            type="button"
            className="btn btn--style-secondary"
            onClick={() => setShowPromptList(!showPromptList)}
          >
            Prompts
          </button>
          
          <button
            type="button"
            className="btn btn--style-secondary"
            onClick={() => setIsExpanded(!isExpanded)}
          >
            {isExpanded ? 'Hide Editor' : 'Edit Prompt'}
          </button>
        </div>
      </div>
      
      {/* Error messages */}
      {error && (
        <div style={{
          color: 'var(--theme-error-500)',
          backgroundColor: 'var(--theme-error-50)',
          padding: '10px',
          borderRadius: '4px',
          marginBottom: '15px',
          border: '1px solid var(--theme-error-150)'
        }}>
          {error}
        </div>
      )}
      
      {!targetField && (
        <div style={{
          color: 'var(--theme-warning-800)',
          backgroundColor: 'var(--theme-warning-50)',
          padding: '10px',
          borderRadius: '4px',
          marginBottom: '15px',
          border: '1px solid var(--theme-warning-150)'
        }}>
          No target field found. Make sure this field is named with format: [fieldName]AIHelper or provide a targetFieldName in admin options.
        </div>
      )}
      
      {/* Generated content preview with full-width textarea to match Payload styling */}
      {showGeneratedContent && (
        <div style={{marginBottom: '20px', width: '100%'}}>
          <label className="field-label" style={{marginBottom: '5px', display: 'block'}}>
            Edit Generated Content:
          </label>
          <textarea
            value={generatedContent}
            onChange={(e) => setGeneratedContent(e.target.value)}
            className="field-type textarea"
            style={{
              width: '100%',
              minHeight: '150px',
              padding: '10px',
              borderRadius: '4px',
              border: '1px solid var(--theme-elevation-150)',
              marginBottom: '10px'
            }}
            rows={8}
          />
          <div style={{display: 'flex', justifyContent: 'flex-end', gap: '10px'}}>
            <button
              type="button"
              className="btn btn--style-secondary"
              onClick={() => {
                setShowGeneratedContent(false);
                setGeneratedContent('');
              }}
            >
              Cancel
            </button>
            <button
              type="button"
              className="btn btn--style-primary"
              onClick={applyGeneratedContent}
            >
              Apply Content
            </button>
          </div>
        </div>
      )}
      
      {/* Prompt list dropdown styled to match Payload */}
      {showPromptList && (
        <div style={{
          marginBottom: '20px',
          padding: '15px',
          border: '1px solid var(--theme-elevation-150)',
          borderRadius: '4px',
          backgroundColor: 'var(--theme-elevation-50)',
          width: '100%'
        }}>
          <div style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            marginBottom: '15px',
            paddingBottom: '10px',
            borderBottom: '1px solid var(--theme-elevation-150)'
          }}>
            <h3 style={{
              fontSize: '16px',
              margin: 0,
              fontWeight: 'bold',
              color: 'var(--theme-elevation-800)'
            }}>
              Saved Prompts
            </h3>
            <button
              type="button"
              className="btn btn--style-secondary"
              onClick={startNewPrompt}
            >
              + New Prompt
            </button>
          </div>
          
          <div style={{maxHeight: '300px', overflowY: 'auto'}}>
            {savedPrompts.length === 0 ? (
              <div style={{
                textAlign: 'center',
                padding: '20px',
                color: 'var(--theme-elevation-500)'
              }}>
                No saved prompts yet. Create your first one!
              </div>
            ) : (
              <ul style={{listStyle: 'none', padding: 0, margin: 0}}>
                {savedPrompts.map(p => (
                  <li 
                    key={p.id}
                    style={{
                      padding: '10px',
                      marginBottom: '10px',
                      borderRadius: '4px',
                      cursor: 'pointer',
                      backgroundColor: p.id === selectedPromptId ? 'var(--theme-elevation-100)' : 'var(--theme-elevation-50)',
                      border: `1px solid ${p.id === selectedPromptId ? 'var(--theme-elevation-400)' : 'var(--theme-elevation-150)'}`,
                      display: 'flex',
                      justifyContent: 'space-between',
                      alignItems: 'flex-start'
                    }}
                    onClick={() => selectPrompt(p.id)}
                  >
                    <div style={{overflow: 'hidden', flex: 1}}>
                      <div style={{
                        fontWeight: 'bold',
                        marginBottom: '5px',
                        color: 'var(--theme-elevation-800)'
                      }}>
                        {p.name}
                      </div>
                      <div style={{
                        fontSize: '13px',
                        color: 'var(--theme-elevation-500)',
                        display: '-webkit-box',
                        WebkitLineClamp: 2,
                        WebkitBoxOrient: 'vertical',
                        overflow: 'hidden'
                      }}>
                        {p.prompt}
                      </div>
                    </div>
                    <button
                      type="button"
                      className="btn btn--icon btn--style-icon btn--delete"
                      aria-label="Delete prompt"
                      onClick={(e) => deletePrompt(p.id, e)}
                      style={{marginLeft: '10px'}}
                    >
                      <svg viewBox="0 0 24 24" height="24" width="24" fill="none" stroke="currentColor" xmlns="http://www.w3.org/2000/svg">
                        <path d="M3 6H5H21" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                        <path d="M8 6V4C8 3.46957 8.21071 2.96086 8.58579 2.58579C8.96086 2.21071 9.46957 2 10 2H14C14.5304 2 15.0391 2.21071 15.4142 2.58579C15.7893 2.96086 16 3.46957 16 4V6M19 6V20C19 20.5304 18.7893 21.0391 18.4142 21.4142C18.0391 21.7893 17.5304 22 17 22H7C6.46957 22 5.96086 21.7893 5.58579 21.4142C5.21071 21.0391 5 20.5304 5 20V6H19Z" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                      </svg>
                    </button>
                  </li>
                ))}
              </ul>
            )}
          </div>
        </div>
      )}
      
      {/* Prompt editor with full-width inputs to match Payload */}
      {isExpanded && (
        <div style={{
          marginTop: '15px',
          width: '100%'
        }}>
          <div style={{marginBottom: '15px', width: '100%'}}>
            <label className="field-label" style={{marginBottom: '5px', display: 'block'}}>
              Prompt Name:
            </label>
            <input
              type="text"
              value={promptName}
              onChange={(e) => setPromptName(e.target.value)}
              className="field-type text"
              placeholder="Enter a descriptive name for this prompt"
              style={{
                width: '100%',
                padding: '10px',
                borderRadius: '4px',
                border: '1px solid var(--theme-elevation-150)'
              }}
            />
          </div>
          
          <div style={{marginBottom: '15px', width: '100%'}}>
            <label className="field-label" style={{marginBottom: '5px', display: 'block'}}>
              Prompt Content:
            </label>
            <textarea
              value={promptText}
              onChange={(e) => setPromptText(e.target.value)}
              className="field-type textarea"
              rows={6}
              placeholder="Write your prompt here. Be specific about the type of content you want to generate."
              style={{
                width: '100%',
                padding: '10px',
                borderRadius: '4px',
                border: '1px solid var(--theme-elevation-150)'
              }}
            />
          </div>
          
          <div style={{
            display: 'flex',
            justifyContent: 'space-between',
            paddingTop: '10px',
            borderTop: '1px solid var(--theme-elevation-150)'
          }}>
            <div>
              {isCreatingNew ? (
                <button
                  type="button"
                  className="btn btn--style-secondary"
                  onClick={() => {
                    setIsCreatingNew(false);
                    if (selectedPromptId) {
                      const prompt = savedPrompts.find(p => p.id === selectedPromptId);
                      if (prompt) {
                        setPromptText(prompt.prompt);
                        setPromptName(prompt.name);
                      }
                    }
                  }}
                >
                  Cancel
                </button>
              ) : (
                <button
                  type="button"
                  className="btn btn--style-secondary"
                  onClick={() => {
                    const defaultPrompt = createDefaultPrompt();
                    if (defaultPrompt) {
                      setPromptText(defaultPrompt.prompt);
                      setPromptName(defaultPrompt.name);
                    }
                  }}
                >
                  Reset to Default
                </button>
              )}
            </div>
            
            <button
              type="button"
              className="btn btn--style-primary"
              onClick={savePrompt}
            >
              {isCreatingNew ? 'Save New Prompt' : 'Update Prompt'}
            </button>
          </div>
        </div>
      )}
      
      {/* Current prompt display */}
      {!isExpanded && (
        <div style={{
          fontSize: '14px',
          color: 'var(--theme-elevation-600)',
          padding: '10px',
          backgroundColor: 'var(--theme-elevation-50)',
          borderRadius: '4px',
          border: '1px solid var(--theme-elevation-150)',
          display: 'flex',
          alignItems: 'center',
          width: '100%'
        }}>
          <span style={{marginRight: '8px', color: 'var(--theme-primary-500)'}}>✨</span>
          <span style={{fontWeight: 'bold', marginRight: '5px', color: 'var(--theme-elevation-800)'}}>
            {promptName}:
          </span>
          <span style={{
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            whiteSpace: 'nowrap'
          }}>
            {promptText}
          </span>
        </div>
      )}
    </div>
  )
}

export default AIHelperField;