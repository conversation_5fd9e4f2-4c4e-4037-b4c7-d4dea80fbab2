/**
 * Redis Storage Adapter
 * Upstash Redis-backed storage for persistent state across serverless functions
 */

import { Redis } from '@upstash/redis';
import { IStorageAdapter } from './types';

export class RedisStorageAdapter implements IStorageAdapter {
  private redis: Redis;
  private prefix: string;
  private ttl: number;

  constructor(prefix: string = 'workflow:', ttl: number = 60 * 60 * 24 * 7) { // 7 days default
    this.redis = new Redis({
      url: process.env.UPSTASH_REDIS_REST_URL || '',
      token: process.env.UPSTASH_REDIS_REST_TOKEN || ''
    });
    this.prefix = prefix;
    this.ttl = ttl;

    // Test Redis connection
    this.testConnection();
  }

  private async testConnection(): Promise<void> {
    try {
      const result = await this.redis.ping();
      console.log(`Redis connection test: ${result}`);
    } catch (error) {
      console.error('Redis connection failed:', error);
    }
  }

  async get(key: string): Promise<any> {
    try {
      const redisKey = this.prefix + key;
      const rawData = await this.redis.json.get<any>(redisKey, "$");

      if (!rawData) {
        return null;
      }

      // Handle the nested array structure from Redis
      let data: any;
      if (Array.isArray(rawData) && rawData.length === 1) {
        data = rawData[0];
      } else {
        data = rawData;
      }

      // Debug Redis state retrieval for monitoring
      if (key === 'system_state' && data) {
        console.log(`🔍 Redis state retrieved:`, {
          workflowsCount: data.workflows ? Object.keys(data.workflows).length : 0,
          executionsCount: data.executions ? Object.keys(data.executions).length : 0
        });
      }

      return data;
    } catch (error) {
      console.error(`Error getting key ${key} from Redis:`, error);
      return null;
    }
  }

  async set(key: string, value: any): Promise<void> {
    try {
      const redisKey = this.prefix + key;
      
      // Store the value in Redis using JSON.SET
      const result = await this.redis.json.set(
        redisKey, 
        "$", 
        value as unknown as Record<string, unknown>
      );

      // Set expiration
      await this.redis.expire(redisKey, this.ttl);

      console.log(`Successfully stored key ${key} in Redis`);
    } catch (error) {
      console.error(`Error setting key ${key} in Redis:`, error);
      throw error;
    }
  }

  async delete(key: string): Promise<void> {
    try {
      const redisKey = this.prefix + key;
      await this.redis.del(redisKey);
      console.log(`Successfully deleted key ${key} from Redis`);
    } catch (error) {
      console.error(`Error deleting key ${key} from Redis:`, error);
      throw error;
    }
  }

  async exists(key: string): Promise<boolean> {
    try {
      const redisKey = this.prefix + key;
      const result = await this.redis.exists(redisKey);
      return result > 0;
    } catch (error) {
      console.error(`Error checking existence of key ${key} in Redis:`, error);
      return false;
    }
  }

  async list(pattern?: string): Promise<string[]> {
    try {
      const searchPattern = pattern ? this.prefix + pattern : this.prefix + '*';
      const keys = await this.redis.keys(searchPattern);
      
      // Remove prefix from keys
      return keys.map(key => key.replace(this.prefix, ''));
    } catch (error) {
      console.error(`Error getting keys with pattern ${pattern} from Redis:`, error);
      return [];
    }
  }

  async clear(): Promise<void> {
    try {
      const keys = await this.redis.keys(this.prefix + '*');
      if (keys.length > 0) {
        await this.redis.del(...keys);
        console.log(`Successfully cleared ${keys.length} keys from Redis`);
      }
    } catch (error) {
      console.error('Error clearing Redis:', error);
      throw error;
    }
  }
}
